{"buildFiles": ["C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\@react-native-community\\slider\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\@react-native-documents\\picker\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\@react-native-google-signin\\google-signin\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-haptic-feedback\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-mmkv\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-mmkv\\android\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-mmkv\\MMKV\\Core\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-share\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-svg\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-view-shot\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\.cxx\\Debug\\7143k2c3\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\.cxx\\Debug\\7143k2c3\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"react_codegen_RNCSlider::@4898bc4726ecf1751b6a": {"artifactName": "react_codegen_RNCSlider", "abi": "arm64-v8a", "output": "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\arm64-v8a\\libreact_codegen_RNCSlider.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd87f9792f5bb7b90eca355eb71bbb68\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}, "react_codegen_rnviewshot::@0ba03d237e60b9258a87": {"artifactName": "react_codegen_rnviewshot", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d": {"artifactName": "react_codegen_RNVectorIconsSpec", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"artifactName": "react_codegen_safeareacontext", "abi": "arm64-v8a", "output": "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd87f9792f5bb7b90eca355eb71bbb68\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"artifactName": "react_codegen_rngesturehandler_codegen", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c": {"artifactName": "react_codegen_RNCWebViewSpec", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_rndocumentpickerCGen::@aac1846a245ca418eb66": {"artifactName": "react_codegen_rndocumentpickerCGen", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_RNDateTimePickerCGen::@59b70ddc31ba2f8ef1d2": {"artifactName": "react_codegen_RNDateTimePickerCGen", "abi": "arm64-v8a", "runtimeFiles": []}, "appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "arm64-v8a", "output": "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\arm64-v8a\\libappmodules.so", "runtimeFiles": ["C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\arm64-v8a\\libreact_codegen_RNCSlider.so", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\arm64-v8a\\libreact_codegen_rnpicker.so", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\arm64-v8a\\libreact-native-mmkv.so", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\arm64-v8a\\libreact_codegen_rnsvg.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd87f9792f5bb7b90eca355eb71bbb68\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}, "react_codegen_RNHapticFeedbackSpec::@a83561f277f6afbb326c": {"artifactName": "react_codegen_RNHapticFeedbackSpec", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65": {"artifactName": "react_codegen_rnsvg", "abi": "arm64-v8a", "output": "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\arm64-v8a\\libreact_codegen_rnsvg.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd87f9792f5bb7b90eca355eb71bbb68\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so"]}, "react_codegen_RNGoogleSignInCGen::@337b7b353bd94a4215c0": {"artifactName": "react_codegen_RNGoogleSignInCGen", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"artifactName": "react_codegen_rnreanimated", "abi": "arm64-v8a", "runtimeFiles": []}, "core::@1b9a7d546b295b7d0867": {"artifactName": "core", "abi": "arm64-v8a", "output": "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\.cxx\\Debug\\7143k2c3\\arm64-v8a\\RNMmkvSpec_cxxmodule_autolinked_build\\core\\libcore.a", "runtimeFiles": []}, "react_codegen_pagerview::@7032a8921530ec438d60": {"artifactName": "react_codegen_pagerview", "abi": "arm64-v8a", "runtimeFiles": []}, "react-native-mmkv::@4ae6a1e65d3e68ba0197": {"artifactName": "react-native-mmkv", "abi": "arm64-v8a", "output": "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\arm64-v8a\\libreact-native-mmkv.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd87f9792f5bb7b90eca355eb71bbb68\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}, "react_codegen_RNImagePickerSpec::@f66ee9a2efecfb28bee4": {"artifactName": "react_codegen_RNImagePickerSpec", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"artifactName": "react_codegen_rnscreens", "abi": "arm64-v8a", "output": "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd87f9792f5bb7b90eca355eb71bbb68\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so"]}, "react_codegen_lottiereactnative::@0fa4dc904d7e359a99fb": {"artifactName": "react_codegen_lottiereactnative", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_RNMmkvSpec::@7541eabbae598da31a69": {"artifactName": "react_codegen_RNMmkvSpec", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_RNShareSpec::@e6c4c2b5aedb2ea213b7": {"artifactName": "react_codegen_RNShareSpec", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_rnpicker::@e8bb2e9e833f47d0d516": {"artifactName": "react_codegen_rnpicker", "abi": "arm64-v8a", "output": "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\arm64-v8a\\libreact_codegen_rnpicker.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd87f9792f5bb7b90eca355eb71bbb68\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so"]}}}