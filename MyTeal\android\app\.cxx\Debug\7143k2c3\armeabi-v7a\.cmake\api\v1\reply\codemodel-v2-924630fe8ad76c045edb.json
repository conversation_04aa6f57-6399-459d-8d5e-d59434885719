{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "RNDateTimePickerCGen_autolinked_build", "jsonFile": "directory-RNDateTimePickerCGen_autolinked_build-Debug-3ff56e28686caabd670d.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni", "targetIndexes": [5]}, {"build": "RNCSlider_autolinked_build", "jsonFile": "directory-RNCSlider_autolinked_build-Debug-12db52c18866453b2981.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/@react-native-community/slider/android/src/main/jni", "targetIndexes": [3]}, {"build": "rndocumentpickerCGen_autolinked_build", "jsonFile": "directory-rndocumentpickerCGen_autolinked_build-Debug-1841aec42bed90879239.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/@react-native-documents/picker/android/build/generated/source/codegen/jni", "targetIndexes": [14]}, {"build": "RNGoogleSignInCGen_autolinked_build", "jsonFile": "directory-RNGoogleSignInCGen_autolinked_build-Debug-0a439cef73bca061534a.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni", "targetIndexes": [6]}, {"build": "rnpicker_autolinked_build", "jsonFile": "directory-rnpicker_autolinked_build-Debug-e08332f6371a556eef85.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/@react-native-picker/picker/android/src/main/jni", "targetIndexes": [16]}, {"build": "lottiereactnative_autolinked_build", "jsonFile": "directory-lottiereactnative_autolinked_build-Debug-70e76c71c89c070003fe.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/lottie-react-native/android/build/generated/source/codegen/jni", "targetIndexes": [12]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-Debug-c089b9a9a9530b06c947.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [15]}, {"build": "RNHapticFeedbackSpec_autolinked_build", "jsonFile": "directory-RNHapticFeedbackSpec_autolinked_build-Debug-5f1d5603b8b6a5909e4e.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni", "targetIndexes": [7]}, {"build": "RNImagePickerSpec_autolinked_build", "jsonFile": "directory-RNImagePickerSpec_autolinked_build-Debug-6f46a7f43c1012f632c6.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni", "targetIndexes": [8]}, {"build": "RNMmkvSpec_autolinked_build", "jsonFile": "directory-RNMmkvSpec_autolinked_build-Debug-b43b5cfbfe85aa1259ac.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni", "targetIndexes": [9]}, {"build": "RNMmkvSpec_cxxmodule_autolinked_build", "childIndexes": [12], "jsonFile": "directory-RNMmkvSpec_cxxmodule_autolinked_build-Debug-bd26f9eb4bc6e2187d1f.json", "minimumCMakeVersion": {"string": "3.9.0"}, "parentIndex": 0, "projectIndex": 1, "source": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-mmkv/android", "targetIndexes": [2]}, {"build": "RNMmkvSpec_cxxmodule_autolinked_build/core", "jsonFile": "directory-RNMmkvSpec_cxxmodule_autolinked_build.core-Debug-3b8b90bc17355f34a53f.json", "minimumCMakeVersion": {"string": "3.10.0"}, "parentIndex": 11, "projectIndex": 2, "source": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-mmkv/MMKV/Core", "targetIndexes": [1]}, {"build": "pagerview_autolinked_build", "jsonFile": "directory-pagerview_autolinked_build-Debug-98ab104d6f98bcc2fdf9.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni", "targetIndexes": [13]}, {"build": "rnreanimated_autolinked_build", "jsonFile": "directory-rnreanimated_autolinked_build-Debug-e0e73edd474168082a92.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni", "targetIndexes": [17]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-ff8ea0a982b82d4782d3.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [21]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-7380660876bc73593654.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [18]}, {"build": "RNShareSpec_autolinked_build", "jsonFile": "directory-RNShareSpec_autolinked_build-Debug-bfcf3917dc0b6d6e9d81.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-share/android/build/generated/source/codegen/jni", "targetIndexes": [10]}, {"build": "rnsvg_autolinked_build", "jsonFile": "directory-rnsvg_autolinked_build-Debug-ae0424ca162e1d867306.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-svg/android/src/main/jni", "targetIndexes": [19]}, {"build": "RNVectorIconsSpec_autolinked_build", "jsonFile": "directory-RNVectorIconsSpec_autolinked_build-Debug-89217fbd34117126f0c2.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni", "targetIndexes": [11]}, {"build": "rnviewshot_autolinked_build", "jsonFile": "directory-rnviewshot_autolinked_build-Debug-1f1ade6ecffcdf2a5128.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni", "targetIndexes": [20]}, {"build": "RNCWebViewSpec_autolinked_build", "jsonFile": "directory-RNCWebViewSpec_autolinked_build-Debug-6adbd2bc81da11b677fe.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-webview/android/build/generated/source/codegen/jni", "targetIndexes": [4]}], "name": "Debug", "projects": [{"childIndexes": [1], "directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 13, 14, 15, 16, 17, 18, 19, 20, 21], "name": "appmodules", "targetIndexes": [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21]}, {"childIndexes": [2], "directoryIndexes": [11], "name": "ReactNativeMmkv", "parentIndex": 0, "targetIndexes": [2]}, {"directoryIndexes": [12], "name": "core", "parentIndex": 1, "targetIndexes": [1]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-1711853eeacd14f79ddc.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 12, "id": "core::@1b9a7d546b295b7d0867", "jsonFile": "target-core-Debug-d6f233459bf1884ef003.json", "name": "core", "projectIndex": 2}, {"directoryIndex": 11, "id": "react-native-mmkv::@4ae6a1e65d3e68ba0197", "jsonFile": "target-react-native-mmkv-Debug-c27a2e76c2e438cde1e6.json", "name": "react-native-mmkv", "projectIndex": 1}, {"directoryIndex": 2, "id": "react_codegen_RNCSlider::@4898bc4726ecf1751b6a", "jsonFile": "target-react_codegen_RNCSlider-Debug-44eea0d890f4a612e443.json", "name": "react_codegen_RNCSlider", "projectIndex": 0}, {"directoryIndex": 21, "id": "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c", "jsonFile": "target-react_codegen_RNCWebViewSpec-Debug-af6111e052c945625c29.json", "name": "react_codegen_RNCWebViewSpec", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_RNDateTimePickerCGen::@59b70ddc31ba2f8ef1d2", "jsonFile": "target-react_codegen_RNDateTimePickerCGen-Debug-6c915759283d72805bb2.json", "name": "react_codegen_RNDateTimePickerCGen", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_RNGoogleSignInCGen::@337b7b353bd94a4215c0", "jsonFile": "target-react_codegen_RNGoogleSignInCGen-Debug-e2195d291219473d9e64.json", "name": "react_codegen_RNGoogleSignInCGen", "projectIndex": 0}, {"directoryIndex": 8, "id": "react_codegen_RNHapticFeedbackSpec::@a83561f277f6afbb326c", "jsonFile": "target-react_codegen_RNHapticFeedbackSpec-Debug-24e71f6526171f0f8bc3.json", "name": "react_codegen_RNHapticFeedbackSpec", "projectIndex": 0}, {"directoryIndex": 9, "id": "react_codegen_RNImagePickerSpec::@f66ee9a2efecfb28bee4", "jsonFile": "target-react_codegen_RNImagePickerSpec-Debug-10c6b9a40585b884f09b.json", "name": "react_codegen_RNImagePickerSpec", "projectIndex": 0}, {"directoryIndex": 10, "id": "react_codegen_RNMmkvSpec::@7541eabbae598da31a69", "jsonFile": "target-react_codegen_RNMmkvSpec-Debug-b38a3117f0e95ae35294.json", "name": "react_codegen_RNMmkvSpec", "projectIndex": 0}, {"directoryIndex": 17, "id": "react_codegen_RNShareSpec::@e6c4c2b5aedb2ea213b7", "jsonFile": "target-react_codegen_RNShareSpec-Debug-818c96e1102e1ecae695.json", "name": "react_codegen_RNShareSpec", "projectIndex": 0}, {"directoryIndex": 19, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d", "jsonFile": "target-react_codegen_RNVectorIconsSpec-Debug-e2f616170ff252efb033.json", "name": "react_codegen_RNVectorIconsSpec", "projectIndex": 0}, {"directoryIndex": 6, "id": "react_codegen_lottiereactnative::@0fa4dc904d7e359a99fb", "jsonFile": "target-react_codegen_lottiereactnative-Debug-7e4702e604d9b7982996.json", "name": "react_codegen_lottiereactnative", "projectIndex": 0}, {"directoryIndex": 13, "id": "react_codegen_pagerview::@7032a8921530ec438d60", "jsonFile": "target-react_codegen_pagerview-Debug-0e95b2f881f1fd72f78f.json", "name": "react_codegen_pagerview", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_rndocumentpickerCGen::@aac1846a245ca418eb66", "jsonFile": "target-react_codegen_rndocumentpickerCGen-Debug-4521ee12810d01668565.json", "name": "react_codegen_rndocumentpickerCGen", "projectIndex": 0}, {"directoryIndex": 7, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-Debug-d2ad9630d761387fd52e.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_rnpicker::@e8bb2e9e833f47d0d516", "jsonFile": "target-react_codegen_rnpicker-Debug-96ab863b44361e64d3db.json", "name": "react_codegen_rnpicker", "projectIndex": 0}, {"directoryIndex": 14, "id": "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a", "jsonFile": "target-react_codegen_rnreanimated-Debug-21d25d65c23a1d576689.json", "name": "react_codegen_rnreanimated", "projectIndex": 0}, {"directoryIndex": 16, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-1c3b0adc539462731984.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 18, "id": "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65", "jsonFile": "target-react_codegen_rnsvg-Debug-4f7ffcfe5c1b6ccf470e.json", "name": "react_codegen_rnsvg", "projectIndex": 0}, {"directoryIndex": 20, "id": "react_codegen_rnviewshot::@0ba03d237e60b9258a87", "jsonFile": "target-react_codegen_rnviewshot-Debug-bc74d82025631d7e2e15.json", "name": "react_codegen_rnviewshot", "projectIndex": 0}, {"directoryIndex": 15, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-765a7af4a9701a300e48.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a", "source": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}