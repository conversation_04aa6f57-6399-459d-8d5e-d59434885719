{"artifacts": [{"path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/build/intermediates/cxx/Debug/7143k2c3/obj/armeabi-v7a/libappmodules.so"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_link_libraries", "target_compile_options", "target_include_directories"], "files": ["C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake", "CMakeLists.txt", "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/@react-native-community/slider/android/src/main/jni/CMakeLists.txt", "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/@react-native-picker/picker/android/src/main/jni/CMakeLists.txt", "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 31, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 56, "parent": 2}, {"command": 2, "file": 0, "line": 101, "parent": 2}, {"command": 2, "file": 0, "line": 87, "parent": 2}, {"command": 3, "file": 0, "line": 63, "parent": 2}, {"command": 4, "file": 0, "line": 58, "parent": 2}, {"file": 2}, {"command": 4, "file": 2, "line": 83, "parent": 8}, {"file": 3}, {"command": 4, "file": 3, "line": 77, "parent": 10}, {"file": 4}, {"command": 4, "file": 4, "line": 83, "parent": 12}, {"file": 5}, {"command": 4, "file": 5, "line": 81, "parent": 14}, {"file": 6}, {"command": 4, "file": 6, "line": 89, "parent": 16}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC"}, {"backtrace": 6, "fragment": "-Wall"}, {"backtrace": 6, "fragment": "-Werror"}, {"backtrace": 6, "fragment": "-Wno-error=cpp"}, {"backtrace": 6, "fragment": "-fexceptions"}, {"backtrace": 6, "fragment": "-frtti"}, {"backtrace": 6, "fragment": "-std=c++20"}, {"backtrace": 6, "fragment": "-DLOG_TAG=\\\"ReactNative\\\""}, {"backtrace": 6, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 4, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 4, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 4, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}], "defines": [{"define": "appmodules_EXPORTS"}], "includes": [{"backtrace": 7, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, {"backtrace": 7, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/build/generated/autolinking/src/main/jni"}, {"backtrace": 9, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/@react-native-community/slider/android/src/main/jni"}, {"backtrace": 11, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/@react-native-picker/picker/android/src/main/jni"}, {"backtrace": 13, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-safe-area-context/android/src/main/jni"}, {"backtrace": 15, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-screens/android/src/main/jni"}, {"backtrace": 17, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-svg/android/src/main/jni"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/@react-native-community/slider/android/src/main/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/@react-native-documents/picker/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/@react-native-documents/picker/android/build/generated/source/codegen/jni/react/renderer/components/rndocumentpickerCGen"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/@react-native-google-signin/google-signin/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleSignInCGen"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/@react-native-picker/picker/android/src/main/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/react/renderer/components/RNHapticFeedbackSpec"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-mmkv/android/../MMKV/Core"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-mmkv/android/../cpp"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-mmkv/MMKV/Core"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-safe-area-context/android/src/main/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-screens/android/src/main/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-share/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-share/android/build/generated/source/codegen/jni/react/renderer/components/RNShareSpec"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-svg/android/src/main/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni/react/renderer/components/rnviewshot"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-webview/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.13/transforms/fd87f9792f5bb7b90eca355eb71bbb68/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.13/transforms/e84d9e10bb52ee5ef536d36432d46174/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.13/transforms/e84d9e10bb52ee5ef536d36432d46174/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include"}], "language": "CXX", "sourceIndexes": [0, 1], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "dependencies": [{"backtrace": 4, "id": "react_codegen_RNDateTimePickerCGen::@59b70ddc31ba2f8ef1d2"}, {"backtrace": 4, "id": "react_codegen_RNCSlider::@4898bc4726ecf1751b6a"}, {"backtrace": 4, "id": "react_codegen_rndocumentpickerCGen::@aac1846a245ca418eb66"}, {"backtrace": 4, "id": "react_codegen_rnpicker::@e8bb2e9e833f47d0d516"}, {"backtrace": 4, "id": "react_codegen_lottiereactnative::@0fa4dc904d7e359a99fb"}, {"backtrace": 4, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec"}, {"backtrace": 4, "id": "react_codegen_RNGoogleSignInCGen::@337b7b353bd94a4215c0"}, {"backtrace": 4, "id": "react_codegen_RNImagePickerSpec::@f66ee9a2efecfb28bee4"}, {"backtrace": 4, "id": "react_codegen_RNHapticFeedbackSpec::@a83561f277f6afbb326c"}, {"backtrace": 4, "id": "react-native-mmkv::@4ae6a1e65d3e68ba0197"}, {"backtrace": 4, "id": "react_codegen_RNMmkvSpec::@7541eabbae598da31a69"}, {"backtrace": 4, "id": "react_codegen_pagerview::@7032a8921530ec438d60"}, {"backtrace": 4, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a"}, {"backtrace": 4, "id": "core::@1b9a7d546b295b7d0867"}, {"backtrace": 4, "id": "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a"}, {"backtrace": 4, "id": "react_codegen_RNShareSpec::@e6c4c2b5aedb2ea213b7"}, {"backtrace": 4, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad"}, {"backtrace": 4, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d"}, {"backtrace": 4, "id": "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65"}, {"backtrace": 4, "id": "react_codegen_rnviewshot::@0ba03d237e60b9258a87"}, {"backtrace": 4, "id": "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c"}], "id": "appmodules::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"backtrace": 4, "fragment": "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\armeabi-v7a\\libreact_codegen_RNCSlider.so", "role": "libraries"}, {"backtrace": 4, "fragment": "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\armeabi-v7a\\libreact_codegen_rnpicker.so", "role": "libraries"}, {"backtrace": 4, "fragment": "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\armeabi-v7a\\libreact-native-mmkv.so", "role": "libraries"}, {"backtrace": 4, "fragment": "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\armeabi-v7a\\libreact_codegen_safeareacontext.so", "role": "libraries"}, {"backtrace": 4, "fragment": "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\armeabi-v7a\\libreact_codegen_rnscreens.so", "role": "libraries"}, {"backtrace": 4, "fragment": "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\armeabi-v7a\\libreact_codegen_rnsvg.so", "role": "libraries"}, {"fragment": "RNMmkvSpec_cxxmodule_autolinked_build\\core\\libcore.a", "role": "libraries"}, {"fragment": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\24\\libz.so", "role": "libraries"}, {"fragment": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\24\\liblog.so", "role": "libraries"}, {"fragment": "-landroid", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd87f9792f5bb7b90eca355eb71bbb68\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "appmodules", "nameOnDisk": "libappmodules.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}, {"name": "Object Libraries", "sourceIndexes": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "OnLoad.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/RNDateTimePickerCGen-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/13e5db451150cc1628879224ca4ec527/RNDateTimePickerCGenJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rndocumentpickerCGen_autolinked_build/CMakeFiles/react_codegen_rndocumentpickerCGen.dir/react/renderer/components/rndocumentpickerCGen/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rndocumentpickerCGen_autolinked_build/CMakeFiles/react_codegen_rndocumentpickerCGen.dir/react/renderer/components/rndocumentpickerCGen/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rndocumentpickerCGen_autolinked_build/CMakeFiles/react_codegen_rndocumentpickerCGen.dir/react/renderer/components/rndocumentpickerCGen/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rndocumentpickerCGen_autolinked_build/CMakeFiles/react_codegen_rndocumentpickerCGen.dir/react/renderer/components/rndocumentpickerCGen/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rndocumentpickerCGen_autolinked_build/CMakeFiles/react_codegen_rndocumentpickerCGen.dir/react/renderer/components/rndocumentpickerCGen/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rndocumentpickerCGen_autolinked_build/CMakeFiles/react_codegen_rndocumentpickerCGen.dir/4d70defca3a237a829093d57a26dbffb/rndocumentpickerCGenJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rndocumentpickerCGen_autolinked_build/CMakeFiles/react_codegen_rndocumentpickerCGen.dir/rndocumentpickerCGen-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/RNGoogleSignInCGen-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/RNGoogleSignInCGenJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNGoogleSignInCGen_autolinked_build/CMakeFiles/react_codegen_RNGoogleSignInCGen.dir/react/renderer/components/RNGoogleSignInCGen/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/lottiereactnative-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/lottiereactnativeJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/RNHapticFeedbackSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/7f9d75c760005477a9a92acb25e45305/RNHapticFeedbackSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNHapticFeedbackSpec_autolinked_build/CMakeFiles/react_codegen_RNHapticFeedbackSpec.dir/react/renderer/components/RNHapticFeedbackSpec/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/RNImagePickerSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/RNImagePickerSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNImagePickerSpec_autolinked_build/CMakeFiles/react_codegen_RNImagePickerSpec.dir/react/renderer/components/RNImagePickerSpec/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/RNMmkvSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/RNMmkvSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/pagerview-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/pagerviewJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNShareSpec_autolinked_build/CMakeFiles/react_codegen_RNShareSpec.dir/RNShareSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNShareSpec_autolinked_build/CMakeFiles/react_codegen_RNShareSpec.dir/react/renderer/components/RNShareSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNShareSpec_autolinked_build/CMakeFiles/react_codegen_RNShareSpec.dir/react/renderer/components/RNShareSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNShareSpec_autolinked_build/CMakeFiles/react_codegen_RNShareSpec.dir/react/renderer/components/RNShareSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNShareSpec_autolinked_build/CMakeFiles/react_codegen_RNShareSpec.dir/react/renderer/components/RNShareSpec/RNShareSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNShareSpec_autolinked_build/CMakeFiles/react_codegen_RNShareSpec.dir/react/renderer/components/RNShareSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNShareSpec_autolinked_build/CMakeFiles/react_codegen_RNShareSpec.dir/react/renderer/components/RNShareSpec/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rnviewshot_autolinked_build/CMakeFiles/react_codegen_rnviewshot.dir/react/renderer/components/rnviewshot/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rnviewshot_autolinked_build/CMakeFiles/react_codegen_rnviewshot.dir/react/renderer/components/rnviewshot/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rnviewshot_autolinked_build/CMakeFiles/react_codegen_rnviewshot.dir/react/renderer/components/rnviewshot/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rnviewshot_autolinked_build/CMakeFiles/react_codegen_rnviewshot.dir/react/renderer/components/rnviewshot/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rnviewshot_autolinked_build/CMakeFiles/react_codegen_rnviewshot.dir/react/renderer/components/rnviewshot/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rnviewshot_autolinked_build/CMakeFiles/react_codegen_rnviewshot.dir/react/renderer/components/rnviewshot/rnviewshotJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/rnviewshot_autolinked_build/CMakeFiles/react_codegen_rnviewshot.dir/rnviewshot-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/armeabi-v7a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}