{"buildFiles": ["C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\@react-native-community\\slider\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\@react-native-documents\\picker\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\@react-native-google-signin\\google-signin\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\@react-native-picker\\picker\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-haptic-feedback\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-mmkv\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-mmkv\\android\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-mmkv\\MMKV\\Core\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-share\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-svg\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-view-shot\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\.cxx\\Debug\\7143k2c3\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\.cxx\\Debug\\7143k2c3\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"appmodules::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "appmodules", "output": "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\armeabi-v7a\\libappmodules.so", "runtimeFiles": ["C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\armeabi-v7a\\libreact_codegen_RNCSlider.so", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\armeabi-v7a\\libreact_codegen_rnpicker.so", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\armeabi-v7a\\libreact-native-mmkv.so", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\armeabi-v7a\\libreact_codegen_safeareacontext.so", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\armeabi-v7a\\libreact_codegen_rnscreens.so", "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\armeabi-v7a\\libreact_codegen_rnsvg.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd87f9792f5bb7b90eca355eb71bbb68\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so"]}, "core::@1b9a7d546b295b7d0867": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "core", "output": "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\.cxx\\Debug\\7143k2c3\\armeabi-v7a\\RNMmkvSpec_cxxmodule_autolinked_build\\core\\libcore.a"}, "react-native-mmkv::@4ae6a1e65d3e68ba0197": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react-native-mmkv", "output": "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\armeabi-v7a\\libreact-native-mmkv.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd87f9792f5bb7b90eca355eb71bbb68\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so"]}, "react_codegen_RNCSlider::@4898bc4726ecf1751b6a": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_RNCSlider", "output": "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\armeabi-v7a\\libreact_codegen_RNCSlider.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd87f9792f5bb7b90eca355eb71bbb68\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so"]}, "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_RNCWebViewSpec"}, "react_codegen_RNDateTimePickerCGen::@59b70ddc31ba2f8ef1d2": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_RNDateTimePickerCGen"}, "react_codegen_RNGoogleSignInCGen::@337b7b353bd94a4215c0": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_RNGoogleSignInCGen"}, "react_codegen_RNHapticFeedbackSpec::@a83561f277f6afbb326c": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_RNHapticFeedbackSpec"}, "react_codegen_RNImagePickerSpec::@f66ee9a2efecfb28bee4": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_RNImagePickerSpec"}, "react_codegen_RNMmkvSpec::@7541eabbae598da31a69": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_RNMmkvSpec"}, "react_codegen_RNShareSpec::@e6c4c2b5aedb2ea213b7": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_RNShareSpec"}, "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_RNVectorIconsSpec"}, "react_codegen_lottiereactnative::@0fa4dc904d7e359a99fb": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_lottiereactnative"}, "react_codegen_pagerview::@7032a8921530ec438d60": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_pagerview"}, "react_codegen_rndocumentpickerCGen::@aac1846a245ca418eb66": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_rndocumentpickerCGen"}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_rngesturehandler_codegen"}, "react_codegen_rnpicker::@e8bb2e9e833f47d0d516": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_rnpicker", "output": "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\armeabi-v7a\\libreact_codegen_rnpicker.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd87f9792f5bb7b90eca355eb71bbb68\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so"]}, "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_rnreanimated"}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_rnscreens", "output": "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\armeabi-v7a\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd87f9792f5bb7b90eca355eb71bbb68\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so"]}, "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_rnsvg", "output": "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\armeabi-v7a\\libreact_codegen_rnsvg.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd87f9792f5bb7b90eca355eb71bbb68\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so"]}, "react_codegen_rnviewshot::@0ba03d237e60b9258a87": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_rnviewshot"}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "react_codegen_safeareacontext", "output": "C:\\Users\\<USER>\\Desktop\\synapys\\MyTeal\\android\\app\\build\\intermediates\\cxx\\Debug\\7143k2c3\\obj\\armeabi-v7a\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd87f9792f5bb7b90eca355eb71bbb68\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}