{"artifacts": [{"path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/build/intermediates/cxx/Debug/7143k2c3/obj/arm64-v8a/libreact-native-mmkv.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "target_include_directories"], "files": ["C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-mmkv/android/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 8, "parent": 0}, {"command": 1, "file": 0, "line": 26, "parent": 0}, {"command": 2, "file": 0, "line": 17, "parent": 0}, {"command": 2, "file": 0, "line": 18, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC"}, {"backtrace": 2, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 2, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 2, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 2, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 2, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 2, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 2, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 2, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}], "defines": [{"define": "react_native_mmkv_EXPORTS"}], "includes": [{"backtrace": 3, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-mmkv/android/../MMKV/Core"}, {"backtrace": 4, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-mmkv/android/../cpp"}, {"backtrace": 2, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-mmkv/MMKV/Core"}, {"backtrace": 2, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/."}, {"backtrace": 2, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.13/transforms/fd87f9792f5bb7b90eca355eb71bbb68/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.13/transforms/e84d9e10bb52ee5ef536d36432d46174/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.13/transforms/e84d9e10bb52ee5ef536d36432d46174/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0, 1, 2], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "dependencies": [{"backtrace": 2, "id": "react_codegen_RNMmkvSpec::@7541eabbae598da31a69"}, {"backtrace": 2, "id": "core::@1b9a7d546b295b7d0867"}], "id": "react-native-mmkv::@4ae6a1e65d3e68ba0197", "link": {"commandFragments": [{"fragment": "-Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"backtrace": 2, "fragment": "RNMmkvSpec_cxxmodule_autolinked_build\\core\\libcore.a", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\24\\liblog.so", "role": "libraries"}, {"backtrace": 2, "fragment": "-landroid", "role": "libraries"}, {"fragment": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\24\\libz.so", "role": "libraries"}, {"fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd87f9792f5bb7b90eca355eb71bbb68\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "role": "libraries"}, {"fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "role": "libraries"}, {"fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e84d9e10bb52ee5ef536d36432d46174\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "react-native-mmkv", "nameOnDisk": "libreact-native-mmkv.so", "paths": {"build": "RNMmkvSpec_cxxmodule_autolinked_build", "source": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-mmkv/android"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2]}, {"name": "Object Libraries", "sourceIndexes": [3, 4, 5, 6, 7, 8, 9]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-mmkv/android/src/main/cpp/AndroidLogger.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-mmkv/cpp/MmkvHostObject.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/node_modules/react-native-mmkv/cpp/NativeMmkvModule.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/arm64-v8a/RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/RNMmkvSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 2, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/arm64-v8a/RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 2, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/arm64-v8a/RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 2, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/arm64-v8a/RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 2, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/arm64-v8a/RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/RNMmkvSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 2, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/arm64-v8a/RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 2, "isGenerated": true, "path": "C:/Users/<USER>/Desktop/synapys/MyTeal/android/app/.cxx/Debug/7143k2c3/arm64-v8a/RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/States.cpp.o", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}